import json
import os
import yaml
import httpx
from src.crawlers.base_crawler import BaseCrawler
from src.crawlers.douyin.endpoints import DouyinAPIEndpoints
from src.crawlers.douyin.util import AwemeId<PERSON><PERSON><PERSON>, BogusManager
from src.crawlers.util import PostDetail
from src.crawlers.exceptions import APIResponseError
from src.utils import get_analyze_logger, config
from src.utils.index import find_url
from src.utils.response import Response
from urllib.parse import urlencode
from pathlib import Path


logger = get_analyze_logger()

# 配置文件路径
# Read the configuration file
path = Path(__file__).parent.parent.parent / "crawlers" / "douyin" / "config.yaml"

# 读取配置文件
with open(f"{path}", "r", encoding="utf-8") as f:
    douyinConfig = yaml.safe_load(f)
    logger.info(f"douyinConfig: {douyinConfig}")


class Douyin:
    def __init__(self, text, type):
        self.url = find_url(text)
        self.text = text
        self.type = type
        self.aweme_id = None
        self.video_data = None
        # 初始化时不执行异步操作，而是在需要时调用

    async def initialize(self):
        """异步初始化方法"""
        try:
            self.aweme_id = await AwemeIdFetcher.get_aweme_id(self.url)
            logger.info(f"aweme_id: {self.aweme_id}")
            self.video_data = await self.fetch_one_video(self.aweme_id)
            logger.info(f"video_data: {self.video_data}")
        except Exception as e:
            logger.error(f"初始化抖音数据时出错: {str(e)}", exc_info=True)
            raise

    # 从配置文件中获取抖音的请求头
    async def get_douyin_headers(self):
        douyin_config = douyinConfig["TokenManager"]["douyin"]
        kwargs = {
            "headers": {
                "Accept-Language": douyin_config["headers"]["Accept-Language"],
                "User-Agent": douyin_config["headers"]["User-Agent"],
                "Referer": douyin_config["headers"]["Referer"],
                "Cookie": douyin_config["headers"]["Cookie"],
            },
            "proxies": {
                "http://": douyin_config["proxies"]["http"],
                "https://": douyin_config["proxies"]["https"],
            },
        }
        return kwargs

     # 获取单个作品数据
    async def fetch_one_video(self, aweme_id: str):
        # 导入反检测模块
        from src.crawlers.douyin.anti_detection import AntiDetectionManager, CookieManager
        import asyncio

        # 获取抖音的实时Cookie
        kwargs = await self.get_douyin_headers()

        # 验证Cookie有效性
        if not CookieManager.validate_cookie_freshness(kwargs["headers"].get("Cookie", "")):
            logger.warning("Cookie可能已过期，建议更新")

        # 添加详细日志输出
        logger.info("=" * 80)
        logger.info("抖音API请求详细信息:")

        # 使用反检测管理器生成真实的参数
        base_params = PostDetail(aweme_id=aweme_id).model_dump()
        realistic_params = AntiDetectionManager.generate_realistic_params(base_params)

        params = PostDetail(aweme_id=aweme_id, **{k: v for k, v in realistic_params.items() if k != 'aweme_id'})
        params_dict = params.model_dump()

        logger.info(f"创建请求参数: {params_dict}")

        # 生成a_bogus签名
        a_bogus = BogusManager.ab_model_2_endpoint(params_dict, kwargs["headers"]["User-Agent"])
        logger.info(f"生成a_bogus: {a_bogus}")

        # 构建完整的请求URL
        endpoint = f"{DouyinAPIEndpoints.POST_DETAIL}?{urlencode(params_dict)}&a_bogus={a_bogus}"

        logger.info(f"完整请求URL: {endpoint}")
        logger.info(f"基础端点: {DouyinAPIEndpoints.POST_DETAIL}")
        logger.info(f"请求参数: {params_dict}")
        logger.info(f"a_bogus签名: {a_bogus}")
        logger.info("请求头信息:")
        for key, value in kwargs["headers"].items():
            if key.lower() == 'cookie':
                # 只显示cookie的前100个字符，避免日志过长
                logger.info(f"  {key}: {value[:100]}...")
            else:
                logger.info(f"  {key}: {value}")
        logger.info(f"代理设置: {kwargs['proxies']}")
        logger.info("=" * 80)

        # 使用反检测管理器添加智能延迟
        delay = AntiDetectionManager.add_timing_jitter()
        await asyncio.sleep(delay)

        # 使用反检测管理器生成真实的请求头
        enhanced_headers = AntiDetectionManager.generate_realistic_headers(kwargs["headers"])

        # 记录反检测尝试
        AntiDetectionManager.log_detection_attempt(endpoint, enhanced_headers, params_dict)

        # 创建一个基础爬虫
        base_crawler = BaseCrawler(proxies=kwargs["proxies"], crawler_headers=enhanced_headers)
        async with base_crawler as crawler:
            response = await crawler.fetch_get_json(endpoint)

            # 验证响应数据
            if AntiDetectionManager.validate_response(response):
                logger.info("响应数据验证通过")
                return response
            else:
                logger.error("响应数据验证失败，可能被检测到")
                # 可以在这里添加重试逻辑或其他处理
                return response

    def to_dict(self):
        """将对象转换为字典，用于 API 返回"""
        try:
            result = {
                "aweme_id": self.aweme_id,
                "video_data": self.video_data,
                # "url": self.url,
                # "final_url": "",
                # "title": self.title,
                # "description": self.description,
                # "image_list": self.image_list,
                # "video": self.video,
                # "app_type": "douyin",
            }
            return Response.success(result, "获取成功")
        except Exception as e:
            logger.error(f"抖音转换为字典时出错: {str(e)}", exc_info=True)
            return Response.error("获取失败")
