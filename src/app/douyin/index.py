import json
import os
import yaml
import httpx
from src.crawlers.base_crawler import BaseCrawler
from src.crawlers.douyin.endpoints import DouyinAPIEndpoints
from src.crawlers.douyin.util import AwemeId<PERSON><PERSON><PERSON>, BogusManager
from src.crawlers.util import PostDetail
from src.crawlers.exceptions import APIResponseError
from src.utils import get_analyze_logger, config
from src.utils.index import find_url
from src.utils.response import Response
from urllib.parse import urlencode
from pathlib import Path


logger = get_analyze_logger()

# 配置文件路径
# Read the configuration file
path = Path(__file__).parent.parent.parent / "crawlers" / "douyin" / "config.yaml"

# 读取配置文件
with open(f"{path}", "r", encoding="utf-8") as f:
    douyinConfig = yaml.safe_load(f)
    logger.info(f"douyinConfig: {douyinConfig}")


class Douyin:
    def __init__(self, text, type):
        self.url = find_url(text)
        self.text = text
        self.type = type
        self.aweme_id = None
        self.video_data = None
        # 初始化时不执行异步操作，而是在需要时调用

    async def initialize(self):
        """异步初始化方法"""
        try:
            self.aweme_id = await AwemeIdFetcher.get_aweme_id(self.url)
            logger.info(f"aweme_id: {self.aweme_id}")
            self.video_data = await self.fetch_one_video(self.aweme_id)
            logger.info(f"video_data: {self.video_data}")
        except Exception as e:
            logger.error(f"初始化抖音数据时出错: {str(e)}", exc_info=True)
            raise

    # 从配置文件中获取抖音的请求头
    async def get_douyin_headers(self):
        douyin_config = douyinConfig["TokenManager"]["douyin"]
        kwargs = {
            "headers": {
                "Accept-Language": douyin_config["headers"]["Accept-Language"],
                "User-Agent": douyin_config["headers"]["User-Agent"],
                "Referer": douyin_config["headers"]["Referer"],
                "Cookie": douyin_config["headers"]["Cookie"],
            },
            "proxies": {
                "http://": douyin_config["proxies"]["http"],
                "https://": douyin_config["proxies"]["https"],
            },
        }
        return kwargs

     # 获取单个作品数据
    async def fetch_one_video(self, aweme_id: str):
        # 获取抖音的实时Cookie
        kwargs = await self.get_douyin_headers()
        # 创建一个基础爬虫
        base_crawler = BaseCrawler(proxies=kwargs["proxies"], crawler_headers=kwargs["headers"])
        async with base_crawler as crawler:
            # 创建一个作品详情的BaseModel参数
            params = PostDetail(aweme_id=aweme_id)
            # 生成一个作品详情的带有加密参数的Endpoint
            # 2024年6月12日22:41:44 由于XBogus加密已经失效，所以不再使用XBogus加密参数，转移至a_bogus加密参数。
            # endpoint = BogusManager.xb_model_2_endpoint(
            #     DouyinAPIEndpoints.POST_DETAIL, params.dict(), kwargs["headers"]["User-Agent"]
            # )

            # 生成一个作品详情的带有a_bogus加密参数的Endpoint
            params_dict = params.dict()
            # params_dict["msToken"] = ''
            a_bogus = BogusManager.ab_model_2_endpoint(params_dict, kwargs["headers"]["User-Agent"])
            endpoint = f"{DouyinAPIEndpoints.POST_DETAIL}?{urlencode(params_dict)}&a_bogus={a_bogus}"

            response = await crawler.fetch_get_json(endpoint)
        return response

    def to_dict(self):
        """将对象转换为字典，用于 API 返回"""
        try:
            result = {
                "aweme_id": self.aweme_id,
                "video_data": self.video_data,
                # "url": self.url,
                # "final_url": "",
                # "title": self.title,
                # "description": self.description,
                # "image_list": self.image_list,
                # "video": self.video,
                # "app_type": "douyin",
            }
            return Response.success(result, "获取成功")
        except Exception as e:
            logger.error(f"抖音转换为字典时出错: {str(e)}", exc_info=True)
            return Response.error("获取失败")
